import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../services/realtime_service.dart';
import '../../../services/market_service.dart';
import '../../symbol/symbol_info_screen.dart';

/// Optimized widget that displays a watchlist item with server-side calculated prices
/// Uses memoization and efficient rebuilding to prevent frame drops
class WatchlistItem extends ConsumerWidget {
  final WatchlistItemDto item;
  final ColorScheme theme;

  const WatchlistItem({super.key, required this.item, required this.theme});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch for real-time price updates
    final latestPrice = ref.watch(latestSymbolPriceProvider(item.symbol));

    // Use real-time price if available, otherwise use server-side cached price
    final currentPrice = latestPrice?.currentPrice ?? item.currentPrice;
    final priceChange = latestPrice?.priceChange ?? item.priceChange;
    final priceChangePercent =
        latestPrice?.priceChangePercent ?? item.priceChangePercent;
    final volume = latestPrice?.volume ?? item.volume;
    final lastUpdated = latestPrice?.lastUpdated ?? item.lastUpdated;
    final status = latestPrice?.status ?? item.status;

    return _WatchlistItemContent(
      item: item,
      theme: theme,
      currentPrice: currentPrice,
      priceChange: priceChange,
      priceChangePercent: priceChangePercent,
      volume: volume,
      lastUpdated: lastUpdated,
      status: status,
    );
  }
}

/// Memoized content widget to prevent unnecessary rebuilds
class _WatchlistItemContent extends StatelessWidget {
  final WatchlistItemDto item;
  final ColorScheme theme;
  final double? currentPrice;
  final double? priceChange;
  final double? priceChangePercent;
  final double? volume;
  final DateTime? lastUpdated;
  final String status;

  const _WatchlistItemContent({
    required this.item,
    required this.theme,
    required this.currentPrice,
    required this.priceChange,
    required this.priceChangePercent,
    required this.volume,
    required this.lastUpdated,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    // Determine price color based on change
    Color priceColor = theme.onSurface;
    if (status == 'error') {
      priceColor = Colors.orange; // Error state
    } else if (priceChange != null) {
      if (priceChange! > 0) {
        priceColor = Colors.green;
      } else if (priceChange! < 0) {
        priceColor = Colors.red;
      }
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) =>
                      SymbolInfoScreen(symbol: item.symbol, initialData: item),
            ),
          );
        },

        // Symbol name
        title: Text(
          item.symbol,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: theme.onSurface,
          ),
        ),

        // Price information
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (status == 'error') ...[
              Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.orange, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    'Price unavailable',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
            ] else if (currentPrice != null) ...[
              Text(
                '\$${currentPrice!.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: priceColor,
                ),
              ),
              const SizedBox(height: 4),
            ] else ...[
              Row(
                children: [
                  SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(
                      strokeWidth: 1,
                      valueColor: AlwaysStoppedAnimation<Color>(theme.primary),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Loading price...',
                    style: TextStyle(
                      fontSize: 14,
                      color: theme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
            ],

            // Price change information
            if (priceChange != null && priceChangePercent != null) ...[
              Row(
                children: [
                  Icon(
                    priceChange! >= 0 ? Icons.trending_up : Icons.trending_down,
                    color: priceColor,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${priceChange! >= 0 ? '+' : ''}${priceChange!.toStringAsFixed(2)} (${priceChangePercent!.toStringAsFixed(2)}%)',
                    style: TextStyle(
                      color: priceColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 2),
            ],

            // Volume and last updated
            Row(
              children: [
                if (volume != null) ...[
                  Text(
                    'Vol: ${_formatVolume(volume!)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: theme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                if (lastUpdated != null) ...[
                  Text(
                    'Updated: ${_formatTime(lastUpdated!)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: theme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),

        // Status indicator
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Connection status indicator
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _getStatusColor(item.status),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              item.status,
              style: TextStyle(
                fontSize: 10,
                color: theme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Format volume for display (e.g., 1.2M, 345.6K)
  String _formatVolume(double volume) {
    if (volume >= 1000000) {
      return '${(volume / 1000000).toStringAsFixed(1)}M';
    } else if (volume >= 1000) {
      return '${(volume / 1000).toStringAsFixed(1)}K';
    } else {
      return volume.toStringAsFixed(0);
    }
  }

  /// Format time for display
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${time.month}/${time.day}';
    }
  }

  /// Get color for status indicator
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'success':
        return Colors.green;
      case 'error':
      case 'failed':
        return Colors.red;
      case 'pending':
      case 'loading':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}

import 'dart:io';

/// Test script to check what the watchlist API returns
void main() async {
  print('=== Testing Watchlist API Response ===');
  
  final baseUrl = 'https://abraapp.undeclab.com/marketdata';
  
  try {
    // Test with a dummy token to see the response structure
    print('\n1. Testing watchlist endpoint with dummy token...');
    final result = await Process.run('curl', [
      '-s', '-v',
      '-H', 'Content-Type: application/json',
      '-H', 'Accept: application/json',
      '-H', 'Authorization: Bearer dummy-token-123',
      '$baseUrl/api/watchlist'
    ]);
    
    print('Response:');
    print('STDOUT: ${result.stdout}');
    print('STDERR: ${result.stderr}');
    print('Exit code: ${result.exitCode}');
    
    // Test the test-db endpoint to see if it exists
    print('\n2. Testing test-db endpoint...');
    final testDbResult = await Process.run('curl', [
      '-s', '-v',
      '-H', 'Content-Type: application/json',
      '-H', 'Accept: application/json',
      '-H', 'Authorization: Bearer dummy-token-123',
      '$baseUrl/api/watchlist/test-db'
    ]);
    
    print('Test-DB Response:');
    print('STDOUT: ${testDbResult.stdout}');
    print('STDERR: ${testDbResult.stderr}');
    print('Exit code: ${testDbResult.exitCode}');
    
  } catch (e) {
    print('Error during testing: $e');
  }
}
